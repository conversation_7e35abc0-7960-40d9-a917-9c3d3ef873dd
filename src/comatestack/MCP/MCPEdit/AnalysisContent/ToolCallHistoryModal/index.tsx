import {Flex} from 'antd';
import {CloseOutlined} from '@ant-design/icons';
import {useState, useEffect, useRef} from 'react';
import {Button, Modal} from '@panda-design/components';
import {MCPToolLogItem} from '@/types/mcp/mcp';
import {IconDownOutlined, IconUpOutlined} from '@/icons/mcp';
import {
    ModalHeader,
    ModalTitle,
    ToolName,
    Divider,
    SectionTitle,
    ContentBlock,
    ContentText,
    CollapsibleSection,
    CollapsibleHeader,
    CollapsibleContent,
} from './styles';

interface Props {
    visible: boolean;
    onClose: () => void;
    toolLogItem: MCPToolLogItem | null;
}

const ToolCallHistoryModal = ({visible, onClose, toolLogItem}: Props) => {
    const [apiRequestExpanded, setApiRequestExpanded] = useState(false);
    const [apiResponseExpanded, setApiResponseExpanded] = useState(false);
    const expandedStateRef = useRef<{
        apiRequest: boolean;
        apiResponse: boolean;
    }>({
        apiRequest: false,
        apiResponse: false,
    });

    useEffect(() => {
        if (visible) {
            setApiRequestExpanded(false);
            setApiResponseExpanded(false);
            expandedStateRef.current = {
                apiRequest: false,
                apiResponse: false,
            };
        }
    }, [visible]);

    const handleApiRequestToggle = () => {
        const newExpanded = !apiRequestExpanded;
        setApiRequestExpanded(newExpanded);
        expandedStateRef.current.apiRequest = newExpanded;
    };

    const handleApiResponseToggle = () => {
        const newExpanded = !apiResponseExpanded;
        setApiResponseExpanded(newExpanded);
        expandedStateRef.current.apiResponse = newExpanded;
    };

    if (!toolLogItem) {
        return null;
    }

    const formatContent = (content: string | undefined) => {
        if (!content) {
            return '';
        }
        try {
            const parsed = JSON.parse(content);
            return JSON.stringify(parsed, null, 2);
        } catch {
            return content;
        }
    };

    return (
        <Modal
            open={visible}
            onCancel={onClose}
            footer={null}
            width={800}
            closable={false}
            styles={{
                body: {
                    padding: 0,
                },
            }}
        >
            <ModalHeader>
                <Flex align="center" gap={12}>
                    <ModalTitle>调用历史</ModalTitle>
                    <Divider />
                    <ToolName>{toolLogItem.toolName}</ToolName>
                </Flex>
                <Button
                    type="text"
                    icon={<CloseOutlined />}
                    onClick={onClose}
                    size="small"
                />
            </ModalHeader>

            <div style={{padding: '20px 4px'}}>
                <div>
                    <SectionTitle>工具输入</SectionTitle>
                    <ContentBlock>
                        <ContentText>
                            {formatContent(toolLogItem.arguments)}
                        </ContentText>
                    </ContentBlock>
                </div>

                <CollapsibleSection>
                    <CollapsibleHeader
                        onClick={handleApiRequestToggle}
                    >
                        <Flex align="center" gap={8}>
                            {apiRequestExpanded ? <IconUpOutlined /> : <IconDownOutlined />}
                            <span>API请求</span>
                        </Flex>
                    </CollapsibleHeader>
                    {apiRequestExpanded && (
                        <CollapsibleContent>
                            <ContentText>
                                {formatContent(toolLogItem.apiRequest)}
                            </ContentText>
                        </CollapsibleContent>
                    )}
                </CollapsibleSection>

                <div style={{marginTop: '36px'}}>
                    <SectionTitle>工具输出</SectionTitle>
                    <ContentBlock>
                        <ContentText>
                            {formatContent(toolLogItem.response)}
                        </ContentText>
                    </ContentBlock>
                </div>

                <CollapsibleSection>
                    <CollapsibleHeader
                        onClick={handleApiResponseToggle}
                    >
                        <Flex align="center" gap={8}>
                            {apiResponseExpanded ? <IconUpOutlined /> : <IconDownOutlined />}
                            <span>API响应</span>
                        </Flex>
                    </CollapsibleHeader>
                    {apiResponseExpanded && (
                        <CollapsibleContent>
                            <ContentText>
                                {formatContent(toolLogItem.apiResponse)}
                            </ContentText>
                        </CollapsibleContent>
                    )}
                </CollapsibleSection>
            </div>
        </Modal>
    );
};

export default ToolCallHistoryModal;
